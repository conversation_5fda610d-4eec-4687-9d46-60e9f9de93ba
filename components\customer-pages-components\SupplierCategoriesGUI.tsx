import { <PERSON>, YStack, H2, H4, XStack, Paragraph, Spacer, Input, Spinner, Text, View, Image } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, ScrollView, Dimensions, ImageBackground } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useState, useMemo, useEffect } from 'react';
import { getCategories, Category } from '../../services/apiService';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

// Helper function to get gradient colors from bgGradient string
const getGradientColors = (bgGradient?: string): string[] => {
    if (!bgGradient) return ['#8F3DD2', '#6B46C1'];

    // Parse Tailwind gradient classes to actual colors
    const gradientMap: Record<string, string[]> = {
        'from-red-500 via-orange-500 to-yellow-500': ['#EF4444', '#F97316', '#EAB308'],
        'from-teal-500 via-cyan-500 to-blue-500': ['#14B8A6', '#06B6D4', '#3B82F6'],
        'from-blue-500 via-indigo-500 to-purple-500': ['#3B82F6', '#6366F1', '#8B5CF6'],
        'from-green-500 via-emerald-500 to-teal-500': ['#22C55E', '#10B981', '#14B8A6'],
    };

    return gradientMap[bgGradient] || ['#8F3DD2', '#6B46C1'];
};

export const SupplierCategoriesGUI = () => {
    const { t } = useTranslation();
    const router = useRouter();
    const [searchQuery, setSearchQuery] = useState('');
    const [categories, setCategories] = useState<Category[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});

    useEffect(() => {
        const fetchCategories = async () => {
            try {
                setLoading(true);
                setError(null);
                const categoriesData = await getCategories();
                setCategories(categoriesData);
            } catch (error) {
                console.error('Error fetching categories:', error);
                setError('Failed to load categories. Please try again.');
                setCategories([]);
            } finally {
                setLoading(false);
            }
        };

        fetchCategories();
    }, []);

    const handleImageError = (categoryKey: string) => {
        setImageErrors(prev => ({ ...prev, [categoryKey]: true }));
    };

    const handleRetry = () => {
        setError(null);
        setLoading(true);
        // Re-trigger the useEffect
        const fetchCategories = async () => {
            try {
                const categoriesData = await getCategories();
                setCategories(categoriesData);
            } catch (error) {
                console.error('Error fetching categories:', error);
                setError('Failed to load categories. Please try again.');
                setCategories([]);
            } finally {
                setLoading(false);
            }
        };
        fetchCategories();
    };

    const filteredCategories = useMemo(() => {
        if (!searchQuery.trim()) return categories;
        return categories.filter(category =>
            t(`categories.${category.key}`, { defaultValue: category.label })
                .toLowerCase()
                .includes(searchQuery.toLowerCase())
        );
    }, [categories, searchQuery, t]);

    if (loading) {
        return (
            <YStack f={1} ai="center" jc="center" bg="$background">
                <Spinner size="large" color="$primary" />
                <Text mt="$3" color="$gray10">
                    {t('categories.loading', { defaultValue: 'Loading categories...' })}
                </Text>
            </YStack>
        );
    }

    if (error) {
        return (
            <YStack f={1} ai="center" jc="center" bg="$background" p="$4">
                <Ionicons name="alert-circle-outline" size={64} color="#EF4444" />
                <Text mt="$3" color="$gray10" ta="center" fontSize="$5" fontWeight="600">
                    {t('categories.error', { defaultValue: 'Oops! Something went wrong' })}
                </Text>
                <Text mt="$2" color="$gray8" ta="center" fontSize="$3">
                    {error}
                </Text>
                <Button
                    mt="$4"
                    onPress={handleRetry}
                    backgroundColor="$primary"
                    color="white"
                    borderRadius="$6"
                    paddingHorizontal="$6"
                    paddingVertical="$3"
                >
                    <XStack ai="center" gap="$2">
                        <Ionicons name="refresh-outline" size={20} color="white" />
                        <Text color="white" fontWeight="600">
                            {t('categories.retry', { defaultValue: 'Try Again' })}
                        </Text>
                    </XStack>
                </Button>
            </YStack>
        );
    }

    return (
        <YStack f={1} bg="$background" width={'100%'} height={'100%'}>
            {/* Header with gradient background */}
            <LinearGradient
                colors={['#8B5CF6', '#6366F1', '#3B82F6']}
                style={{ paddingTop: 60, paddingBottom: 30, paddingHorizontal: 20 }}
            >
                <YStack gap="$3">
                    <H2 color="white" fontWeight="bold" fontSize="$8">
                        {t('categories.chooseCategory', { defaultValue: 'Choose a Category' })}
                    </H2>
                    <XStack ai="center" gap="$2">
                        <Ionicons name="location-outline" size={18} color="white" />
                        <Paragraph size="$3" color="rgba(255,255,255,0.9)">
                            {t('location.nablus', { defaultValue: 'Nablus, Palestine' })}
                        </Paragraph>
                    </XStack>

                    {/* Search */}
                    <Input
                        placeholder={t('categories.searchSuppliers', { defaultValue: 'Search categories...' })}
                        size="$4"
                        borderWidth={0}
                        backgroundColor="rgba(255,255,255,0.15)"
                        color="white"
                        placeholderTextColor="rgba(255,255,255,0.7)"
                        borderRadius="$6"
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        mt="$3"
                    />
                </YStack>
            </LinearGradient>

            {/* Categories Grid */}
            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ padding: 20, paddingBottom: 40 }}
                style={{ flex: 1 }}
            >
                <YStack gap="$4">
                    {filteredCategories.length === 0 ? (
                        <YStack ai="center" jc="center" py="$8">
                            <Ionicons name="search-outline" size={48} color="#9CA3AF" />
                            <Text mt="$3" color="$gray10" ta="center">
                                {t('categories.noResults', { defaultValue: 'No categories found' })}
                            </Text>
                        </YStack>
                    ) : (
                        <XStack fw="wrap" jc="space-between" gap="$3">
                            {filteredCategories.map((category, index) => {
                                const gradientColors = getGradientColors(category.bgGradient);
                                return (
                                    <Pressable
                                        key={category.key}
                                        onPress={() => router.push(category.route as any)}
                                        style={({ pressed }) => ({
                                            width: (width - 60) / 2,
                                            transform: pressed ? [{ scale: 0.95 }] : [{ scale: 1 }],
                                            marginBottom: 16
                                        })}
                                    >
                                        <Card
                                            pointerEvents="none"
                                            borderRadius="$6"
                                            borderWidth={0}
                                            overflow="hidden"
                                            elevation={8}
                                            shadowColor="$shadowColor"
                                            shadowOffset={{ width: 0, height: 4 }}
                                            shadowOpacity={0.15}
                                            shadowRadius={12}
                                            height={200}
                                        >
                                            <LinearGradient
                                                colors={gradientColors}
                                                style={{ flex: 1, padding: 16 }}
                                            >
                                                {/* Badge */}
                                                {category.badge && (
                                                    <View
                                                        position="absolute"
                                                        top={12}
                                                        right={12}
                                                        backgroundColor="rgba(255,255,255,0.9)"
                                                        borderRadius="$4"
                                                        paddingHorizontal="$2"
                                                        paddingVertical="$1"
                                                        zIndex={10}
                                                    >
                                                        <Text fontSize="$1" fontWeight="600" color={gradientColors[0]}>
                                                            {category.badge}
                                                        </Text>
                                                    </View>
                                                )}

                                                {/* Category Image */}
                                                <YStack f={1} ai="center" jc="center">
                                                    {category.image && !imageErrors[category.key] ? (
                                                        <Image
                                                            source={{ uri: category.image }}
                                                            width={80}
                                                            height={80}
                                                            borderRadius="$4"
                                                            backgroundColor="rgba(255,255,255,0.1)"
                                                            onError={() => handleImageError(category.key)}
                                                        />
                                                    ) : (
                                                        <View
                                                            width={80}
                                                            height={80}
                                                            backgroundColor="rgba(255,255,255,0.2)"
                                                            borderRadius="$4"
                                                            ai="center"
                                                            jc="center"
                                                        >
                                                            <Ionicons
                                                                name={category.icon as any}
                                                                size={40}
                                                                color="white"
                                                            />
                                                        </View>
                                                    )}
                                                </YStack>

                                                {/* Category Info */}
                                                <YStack gap="$1" mt="$3">
                                                    <H4 color="white" fontWeight="bold" ta="center" fontSize="$5">
                                                        {t(`categories.${category.key}`, { defaultValue: category.label })}
                                                    </H4>
                                                    {category.subtitle && (
                                                        <Text
                                                            color="rgba(255,255,255,0.8)"
                                                            ta="center"
                                                            fontSize="$2"
                                                            numberOfLines={2}
                                                        >
                                                            {category.subtitle}
                                                        </Text>
                                                    )}
                                                </YStack>
                                            </LinearGradient>
                                        </Card>
                                    </Pressable>
                                );
                            })}
                        </XStack>
                    )}
                </YStack>
            </ScrollView>
        </YStack>
    );
};