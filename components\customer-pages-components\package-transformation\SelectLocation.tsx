import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, YStack, XStack } from 'tamagui';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import { useSetSendPackage } from './useSendPackageStore';
import { useUpdateRequestPickup } from './useRequestPickupStore';
import { useSetOrderAddress } from '../useLastOrderStore';
import { Dimensions, Platform } from 'react-native';
import MapView, { Marker } from 'react-native-maps';

export default function SelectLocation() {
  const router = useRouter();
  const { type } = useLocalSearchParams<{ type: 'pickup' | 'dropoff' | 'request' | 'orderaddress' }>();
  const setPickup = useSetSendPackage((s) => s.setPickup);
  const setDropoff = useSetSendPackage((s) => s.setDropoff);
  const updateField = useUpdateRequestPickup((r) => r.updateField);
  const setOrderAddress = useSetOrderAddress((o) => o.setAddress);

  console.log('🗺️ SelectLocation component loaded');
  console.log('🗺️ Type parameter:', type);

  const [marker, setMarker] = useState<[number, number] | null>(null); // [lng, lat]
  const [region, setRegion] = useState<[number, number] | null>(null);
  const [address, setAddress] = useState<string | null>(null);
  const [addressLoading, setAddressLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [mapError, setMapError] = useState(false);
  const [fallbackMode, setFallbackMode] = useState(false);
  const { width, height } = Dimensions.get('window');

  useEffect(() => {
    console.log('🗺️ Starting location setup...');
    (async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        console.log('🗺️ Location permission status:', status);

        if (status !== 'granted') {
          console.log('🗺️ Location permission denied. Using default Nablus location.');
          alert('Location permission denied. Defaulting to Nablus.');
          const lngLat: [number, number] = [35.2544, 32.2211];
          setRegion(lngLat);
          setMarker(lngLat);
          reverseGeocode(lngLat[1], lngLat[0]);
          setLoading(false);
          return;
        }

        console.log('🗺️ Getting current position...');
        const loc = await Location.getCurrentPositionAsync({});
        console.log('🗺️ Current location:', loc.coords);
        const lngLat: [number, number] = [loc.coords.longitude, loc.coords.latitude];
        setRegion(lngLat);
        setMarker(lngLat);
        reverseGeocode(loc.coords.latitude, loc.coords.longitude);
        setLoading(false);
        console.log('🗺️ Location setup complete');
      } catch (error) {
        console.error('🗺️ Error setting up location:', error);
        // Fallback to Nablus
        const lngLat: [number, number] = [35.2544, 32.2211];
        setRegion(lngLat);
        setMarker(lngLat);
        setLoading(false);
      }
    })();
  }, []);

  const reverseGeocode = async (lat: number, lng: number) => {
    try {
      setAddressLoading(true);
      const geo = await Location.reverseGeocodeAsync({ latitude: lat, longitude: lng });
      if (!geo || geo.length === 0) {
        setAddress(`Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`);
        return;
      }
      const g = geo[0];
      const formatted = `${g.name || ''}, ${g.city || ''}, ${g.region || ''}`.trim();
      setAddress(formatted || `Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`);
    } catch (e) {
      setAddress(`Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`);
    } finally {
      setAddressLoading(false);
    }
  };

  const onMapPress = (e: any) => {
    const { latitude, longitude } = e.nativeEvent.coordinate;
    const coords: [number, number] = [longitude, latitude];
    setMarker(coords);
    reverseGeocode(latitude, longitude);
  };

  const handleSelect = () => {
    if (!marker) return;
    const [lng, lat] = marker;

    const locationData = {
      lat,
      lng,
      address: address || `Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`,
    };

    if (type === 'pickup') setPickup(locationData);
    else if(type === 'dropoff') setDropoff(locationData);
    else if(type === 'request') updateField('pickup', locationData);
    else setOrderAddress(locationData);

    router.back();
  };

  if (loading || !region) {
    console.log('🗺️ Still loading... loading:', loading, 'region:', region);
    return (
      <View flex={1} ai="center" jc="center">
        <Spinner size="large" color="$primary" />
        <Text mt="$3">Fetching location...</Text>
        <Text mt="$2" fontSize="$3" color="$gray10">
          Loading: {loading ? 'true' : 'false'}, Region: {region ? 'set' : 'null'}
        </Text>
      </View>
    );
  }

  console.log('🗺️ Rendering map with region:', region);

  // Fallback mode for when map fails
  if (fallbackMode || mapError) {
    return (
      <View style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
        <YStack flex={1} p="$4" gap="$4" jc="center">
          <Text fontSize="$6" fontWeight="bold" textAlign="center">
            📍 Select Location
          </Text>

          <Text fontSize="$4" textAlign="center" color="$gray10">
            Map is not available. Please enter your address manually:
          </Text>

          <YStack gap="$3">
            <Button
              size="$5"
              bg="$blue10"
              color="white"
              onPress={() => {
                const nablus = {
                  lat: 32.2211,
                  lng: 35.2544,
                  address: "Nablus, Palestine"
                };
                if (type === 'pickup') setPickup(nablus);
                else if(type === 'dropoff') setDropoff(nablus);
                else if(type === 'request') updateField('pickup', nablus);
                else setOrderAddress(nablus);
                router.back();
              }}
            >
              📍 Use Nablus (Default)
            </Button>

            <Button
              size="$5"
              bg="$green10"
              color="white"
              onPress={() => {
                const ramallah = {
                  lat: 31.9073,
                  lng: 35.2044,
                  address: "Ramallah, Palestine"
                };
                if (type === 'pickup') setPickup(ramallah);
                else if(type === 'dropoff') setDropoff(ramallah);
                else if(type === 'request') updateField('pickup', ramallah);
                else setOrderAddress(ramallah);
                router.back();
              }}
            >
              📍 Use Ramallah
            </Button>

            <Button
              size="$4"
              bg="$gray8"
              color="white"
              onPress={() => setFallbackMode(false)}
            >
              🔄 Try Map Again
            </Button>

            <Button
              size="$4"
              bg="$red10"
              color="white"
              onPress={() => router.back()}
            >
              ❌ Cancel
            </Button>
          </YStack>
        </YStack>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      {/* Debug Info */}
      <View style={{
        position: 'absolute',
        top: 50,
        left: 10,
        right: 10,
        zIndex: 1000,
        backgroundColor: 'rgba(0,0,0,0.7)',
        padding: 10,
        borderRadius: 8
      }}>
        <Text color="white" fontSize="$2">
          Debug: Type={type}, Region={region ? `${region[1]}, ${region[0]}` : 'null'}
        </Text>
        <Text color="white" fontSize="$2">
          Marker: {marker ? `${marker[1]}, ${marker[0]}` : 'null'}
        </Text>
        <Button
          size="$2"
          bg="$orange10"
          color="white"
          onPress={() => setFallbackMode(true)}
          mt="$2"
        >
          🔧 Use Fallback Mode
        </Button>
      </View>

      {/* Real Map for Location Selection */}
      <MapView
        style={{ width, height }}
        region={{
          latitude: region[1],
          longitude: region[0],
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
        onPress={onMapPress}
        showsUserLocation={true}
        showsMyLocationButton={true}
        onMapReady={() => console.log('🗺️ Map is ready!')}
        onRegionChange={(region) => console.log('🗺️ Region changed:', region)}
        onError={(error) => {
          console.error('🗺️ Map error:', error);
          setMapError(true);
        }}
      >
        {/* Selected Marker */}
        {marker && (
          <Marker
            coordinate={{
              latitude: marker[1],
              longitude: marker[0],
            }}
            pinColor="purple"
          />
        )}
      </MapView>

      <YStack
        position="absolute"
        bottom={30}
        width="100%"
        px="$4"
        gap="$3"
        zIndex={10}
        pointerEvents="box-none"
      >
        {addressLoading ? (
          <Text fontSize="$5" textAlign="center" color="$gray10">
            Fetching address...
          </Text>
        ) : address ? (
          <Text fontSize="$5" textAlign="center" color="$primary">
            {address}
          </Text>
        ) : null}

        <Button
          size="$5"
          bg="$primary"
          color="white"
          icon={<Ionicons name="checkmark" size={20} color="white" />}
          disabled={!marker}
          onPress={() => {
            console.log('🗺️ Confirm Location clicked');
            handleSelect();
          }}
          hoverStyle={{ bg: '$third' }}
          pressStyle={{ bg: '$third' }}
        >
          Confirm Location
        </Button>

        {/* Test Button */}
        <Button
          size="$4"
          bg="$red10"
          color="white"
          onPress={() => {
            console.log('🗺️ Test button clicked - going back');
            router.back();
          }}
        >
          🧪 Test - Go Back
        </Button>
      </YStack>
    </View>
  );
}
