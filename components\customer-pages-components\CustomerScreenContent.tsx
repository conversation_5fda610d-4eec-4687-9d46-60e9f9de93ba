import React from 'react';
import { useLocalSearchParams } from 'expo-router';
import { useState, useMemo, useEffect } from 'react';
import { getSuppliersByCategory, getSupplierById, Supplier } from '../../services/apiService';
import { YStack, Text } from 'tamagui';
import { ActivityIndicator } from 'react-native';
import { CustomerProfileGUI } from './CustomerProfileGUI';
import { CustomerHomeGUI } from './CustomerHomeGUI';
import { SupplierCategoriesGUI } from './SupplierCategoriesGUI';
import { SuppliersPageGUI } from './SuppliersPageGUI';
import { SupplierDetailsGUI } from './SupplierDetailsGUI';
import { SupplierProductDetailsGUI } from './SupplierProductDetailsGUI';
import { CheckoutPage } from './OrderCheckout';
import { OrderConfirmation } from './OrderConfirmation';
import { CustomerOrdersGUI } from './orders-page-components/CustomerOrdersGUI';
import { OrderDetails } from './orders-page-components/OrderDetails';
import OrderTracking from './orders-page-components/OrderTracking';
import { SendPackageForm } from './package-transformation/SendPackageForm';
import { SendPackageConfirmation } from './package-transformation/SendPackageConfirmation';
import SelectLocation from './package-transformation/SelectLocation';
import { RequestPickupForm } from './package-transformation/RequestPickupForm';
import { RequestPickupConfirmation}  from './package-transformation/RequestPickupConfirmation';
import { CustomerPackagesGUI } from './package-transformation/CustomerPackagesGUI';
import PackageTracking from './package-transformation/PackageTracking';
import SuppliersMap from './SuppliersMap';
import { AIChatGUI } from './AIChatGUI';

type CustomerScreenContentProps = {
  title: string;
  path: string;
  children?: React.ReactNode;
};

export const CustomerScreenContent = ({ title, path, children }: CustomerScreenContentProps) => {
  const { category } = useLocalSearchParams<{ category: string }>();
  const { supplierId } = useLocalSearchParams<{ supplierId: string }>();
  const [search, setSearch] = useState('');
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [currentSupplier, setCurrentSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch suppliers when category changes
  useEffect(() => {
    const fetchSuppliers = async () => {
      if (!category) return;

      try {
        setLoading(true);
        const { suppliers: suppliersData } = await getSuppliersByCategory(category);
        setSuppliers(suppliersData);
      } catch (error) {
        console.error('Error fetching suppliers:', error);
        setSuppliers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSuppliers();
  }, [category]);

  // Fetch current supplier when supplierId changes
  useEffect(() => {
    const fetchCurrentSupplier = async () => {
      if (!supplierId) {
        setCurrentSupplier(suppliers[0] || null);
        return;
      }

      try {
        const supplier = await getSupplierById(supplierId);
        setCurrentSupplier(supplier);
      } catch (error) {
        console.error('Error fetching supplier:', error);
        setCurrentSupplier(suppliers[0] || null);
      }
    };

    if (suppliers.length > 0 || supplierId) {
      fetchCurrentSupplier();
    }
  }, [supplierId, suppliers]);

  // For now, we'll remove promotions filtering since we don't have a promotions API yet
  const filteredPromotions: any[] = [];

  const [selectedCategory, setSelectedCategory] = useState('All');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Type guard for products with category property
  function hasCategory(p: any): p is { category: string; name: string } {
    return typeof p.category === 'string' && typeof p.name === 'string';
  }

  // Default products for restaurants when no products are available
  const getDefaultProducts = (supplierCategory: string, supplierId: string) => {
    if (supplierCategory === 'restaurants') {
      return [
        {
          id: `${supplierId}-default-1`,
          name: 'شاورما لحم',
          image: 'https://images.unsplash.com/photo-1529006557810-274b9b2fc783?w=400&h=400&fit=crop',
          price: 18,
          category: 'Shawarma',
          restaurantOptions: {
            additions: [
              { id: 'extra-meat', name: 'زيادة لحم', price: 5 },
              { id: 'extra-sauce', name: 'زيادة صوص', price: 2 }
            ],
            without: ['بصل', 'مخللات'],
            sides: [
              { id: 'fries', name: 'بطاطا مقلية', price: 8 },
              { id: 'drink', name: 'مشروب غازي', price: 5 }
            ]
          }
        },
        {
          id: `${supplierId}-default-2`,
          name: 'شاورما دجاج',
          image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=400&fit=crop',
          price: 16,
          category: 'Shawarma',
          restaurantOptions: {
            additions: [
              { id: 'extra-chicken', name: 'زيادة دجاج', price: 4 },
              { id: 'extra-garlic', name: 'زيادة ثوم', price: 1 }
            ],
            without: ['بصل', 'خس'],
            sides: [
              { id: 'fries', name: 'بطاطا مقلية', price: 8 },
              { id: 'drink', name: 'مشروب غازي', price: 5 }
            ]
          }
        },
        {
          id: `${supplierId}-default-3`,
          name: 'فلافل',
          image: 'https://images.unsplash.com/photo-1593504049359-74330189a345?w=400&h=400&fit=crop',
          price: 12,
          category: 'Sandwiches',
          restaurantOptions: {
            additions: [
              { id: 'extra-falafel', name: 'زيادة فلافل', price: 3 },
              { id: 'extra-tahini', name: 'زيادة طحينية', price: 1 }
            ],
            without: ['طماطم', 'خيار'],
            sides: [
              { id: 'fries', name: 'بطاطا مقلية', price: 8 },
              { id: 'drink', name: 'مشروب غازي', price: 5 }
            ]
          }
        }
      ];
    }
    return [];
  };

  const filteredProducts = useMemo(() => {
    console.log('=== FILTERING PRODUCTS ===');
    console.log('currentSupplier:', currentSupplier);
    console.log('currentSupplier?.products:', currentSupplier?.products);
    console.log('currentSupplier?.category:', currentSupplier?.category);

    let products = currentSupplier?.products || [];

    // If no products from backend, use default products for restaurants
    if (products.length === 0 && currentSupplier) {
      console.log('No products found, generating defaults for category:', currentSupplier.category);
      products = getDefaultProducts(currentSupplier.category, currentSupplier.id);
      console.log('Generated default products:', products);
    }

    console.log('All products before filtering:', products);
    console.log('Selected category:', selectedCategory);
    console.log('Search query:', searchQuery);

    products = products.filter(hasCategory);
    console.log('After hasCategory filter:', products);

    if (selectedCategory !== 'All') {
      products = products.filter(p => p.category === selectedCategory);
      console.log('After category filter:', products);
    }
    if (searchQuery) {
      products = products.filter(p => p.name.toLowerCase().includes(searchQuery.toLowerCase()));
      console.log('After search filter:', products);
    }

    console.log('Final filtered products:', products);
    console.log('=== END FILTERING ===');
    return products;
  }, [currentSupplier?.products, currentSupplier?.category, currentSupplier?.id, selectedCategory, searchQuery]);

  const supplierPromotions = currentSupplier?.products?.filter(p =>
    typeof p.discountPrice === 'number' && p.discountPrice > 0
  ) || [];

  // extract categories from currentSupplier->products
  const supplierCategories = useMemo(() => {
    let products = currentSupplier?.products || [];

    // If no products from backend, use default products for restaurants
    if (products.length === 0 && currentSupplier) {
      products = getDefaultProducts(currentSupplier.category, currentSupplier.id);
    }

    if (products.length === 0) return ['All'];
    const allCategories = products.map(p => p.category);
    return ['All', ...new Set(allCategories)];
  }, [currentSupplier?.products, currentSupplier?.category, currentSupplier?.id]);

  const filteredSuppliers = suppliers
    .filter(s => s.name.toLowerCase().includes(search.toLowerCase()));

  return ((title==="CustomerProfile") ? (
      <CustomerProfileGUI title={title} />
    ) : (title==="SuppliersMap") ? (
      <SuppliersMap />
    ) : (title==="CustomerOrders") ? (
      <CustomerOrdersGUI />
    ) : (title==="CustomerPackages") ? (
      <CustomerPackagesGUI />
    ) : (title==="OrderDetails") ? (
      <OrderDetails />
    ) : (title==="OrderTracking") ? (
      <OrderTracking />
    ) : (title==="CustomerHome") ? (
      <CustomerHomeGUI />
    ) : (title==="SupplierCategories") ? (
      <SupplierCategoriesGUI />
    ) : (title==="SuppliersPage") ? (
      <SuppliersPageGUI 
        category={category} 
        search={search} 
        setSearch={setSearch} 
        filteredSuppliers={filteredSuppliers} 
        filteredPromotions={filteredPromotions} 
      />
    ) : (title==="SupplierDetails") ? (
      currentSupplier ? (
        <SupplierDetailsGUI
          currentSupplier={currentSupplier}
          supplierPromotions={supplierPromotions ?? []}
          setShowFilters={setShowFilters}
          supplierCategories={supplierCategories}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          filteredProducts={filteredProducts}
        />
      ) : (
        <YStack
          flex={1}
          justifyContent="center"
          alignItems="center"
          gap="$4"
          padding="$4"
        >
          {loading ? (
            <>
              <Text fontSize="$5">Loading supplier details...</Text>
              <ActivityIndicator size="large" color="#3498db" />
            </>
          ) : (
            <Text fontSize="$5" color="$red10">Supplier not found</Text>
          )}
        </YStack>
      )
    ) : (title==="SupplierProductDetails") ? (
      <SupplierProductDetailsGUI 
        category={category || ''}
      />
    ) : (title==="OrderCheckout") ? (
      <CheckoutPage category={category || ''}/>
    ) : (title==="OrderConfirmation") ? (
      <OrderConfirmation />
    ) : (title==="SendPackage") ? (
      <SendPackageForm />
    ) : (title==="SendPackageConfirmation") ? (
      <SendPackageConfirmation />
      
    ) : (title==="RequestPickup") ? (
      <RequestPickupForm />
      
    ) : (title==="RequestPickupConfirmation") ? (
      <RequestPickupConfirmation />
      
    ) : (title==="SelectLocation") ? (
      <SelectLocation />
    ) : (title==="AIChat") ? (
      <AIChatGUI />
    ) : (
      <PackageTracking />
    )
  );
};