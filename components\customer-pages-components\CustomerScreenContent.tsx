import React from 'react';
import { useLocalSearchParams } from 'expo-router';
import { useState, useMemo, useEffect } from 'react';
import { getSuppliersByCategory, getSupplierById, Supplier } from '../../services/apiService';
import { YStack, Text } from 'tamagui';
import { ActivityIndicator } from 'react-native';
import { CustomerProfileGUI } from './CustomerProfileGUI';
import { CustomerHomeGUI } from './CustomerHomeGUI';
import { SupplierCategoriesGUI } from './SupplierCategoriesGUI';
import { SuppliersPageGUI } from './SuppliersPageGUI';
import { SupplierDetailsGUI } from './SupplierDetailsGUI';
import { SupplierProductDetailsGUI } from './SupplierProductDetailsGUI';
import { CheckoutPage } from './OrderCheckout';
import { OrderConfirmation } from './OrderConfirmation';
import { CustomerOrdersGUI } from './orders-page-components/CustomerOrdersGUI';
import { OrderDetails } from './orders-page-components/OrderDetails';
import OrderTracking from './orders-page-components/OrderTracking';
import { SendPackageForm } from './package-transformation/SendPackageForm';
import { SendPackageConfirmation } from './package-transformation/SendPackageConfirmation';
import SelectLocation from './package-transformation/SelectLocation';
import { RequestPickupForm } from './package-transformation/RequestPickupForm';
import { RequestPickupConfirmation}  from './package-transformation/RequestPickupConfirmation';
import { CustomerPackagesGUI } from './package-transformation/CustomerPackagesGUI';
import PackageTracking from './package-transformation/PackageTracking';
import SuppliersMap from './SuppliersMap';
import { AIChatGUI } from './AIChatGUI';

type CustomerScreenContentProps = {
  title: string;
  path: string;
  children?: React.ReactNode;
};

export const CustomerScreenContent = ({ title, path, children }: CustomerScreenContentProps) => {
  const { category } = useLocalSearchParams<{ category: string }>();
  const { supplierId } = useLocalSearchParams<{ supplierId: string }>();
  const [search, setSearch] = useState('');
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [currentSupplier, setCurrentSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch suppliers when category changes
  useEffect(() => {
    const fetchSuppliers = async () => {
      if (!category) return;

      try {
        setLoading(true);
        const { suppliers: suppliersData } = await getSuppliersByCategory(category);
        setSuppliers(suppliersData);
      } catch (error) {
        console.error('Error fetching suppliers:', error);
        setSuppliers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSuppliers();
  }, [category]);

  // Fetch current supplier when supplierId changes
  useEffect(() => {
    const fetchCurrentSupplier = async () => {
      if (!supplierId) {
        const fallbackSupplier = suppliers[0] || null;
        if (fallbackSupplier && !fallbackSupplier.category) {
          fallbackSupplier.category = 'restaurants';
        }
        console.log('Using fallback supplier:', fallbackSupplier);
        setCurrentSupplier(fallbackSupplier);
        return;
      }

      try {
        // Use the dedicated products API to get supplier with products
        const supplierData = await getSupplierProducts(supplierId);
        const supplier = {
          ...supplierData.supplier,
          products: supplierData.products,
          category: 'restaurants' // Ensure category is set
        };
        console.log('Fetched supplier with products:', supplier);
        setCurrentSupplier(supplier);
      } catch (error) {
        console.error('Error fetching supplier products:', error);
        // Fallback to basic supplier info
        try {
          const supplier = await getSupplierById(supplierId);
          if (supplier && !supplier.category) {
            supplier.category = 'restaurants';
          }
          console.log('Fetched basic supplier:', supplier);
          setCurrentSupplier(supplier);
        } catch (fallbackError) {
          console.error('Error fetching basic supplier:', fallbackError);
          const fallbackSupplier = suppliers[0] || null;
          if (fallbackSupplier && !fallbackSupplier.category) {
            fallbackSupplier.category = 'restaurants';
          }
          setCurrentSupplier(fallbackSupplier);
        }
      }
    };

    if (suppliers.length > 0 || supplierId) {
      fetchCurrentSupplier();
    }
  }, [supplierId, suppliers]);

  // For now, we'll remove promotions filtering since we don't have a promotions API yet
  const filteredPromotions: any[] = [];

  const [selectedCategory, setSelectedCategory] = useState('All');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Type guard for products with category property
  function hasCategory(p: any): p is { category: string; name: string } {
    return typeof p.category === 'string' && typeof p.name === 'string';
  }



  const filteredProducts = useMemo(() => {
    if (!currentSupplier) {
      return [];
    }

    let products = currentSupplier?.products || [];
    console.log('Products from backend:', products);

    // Filter products
    products = products.filter(hasCategory);
    if (selectedCategory !== 'All') {
      products = products.filter(p => p.category === selectedCategory);
    }
    if (searchQuery) {
      products = products.filter(p => p.name.toLowerCase().includes(searchQuery.toLowerCase()));
    }

    return products;
  }, [currentSupplier?.products, selectedCategory, searchQuery]);

  const supplierPromotions = currentSupplier?.products?.filter(p =>
    typeof p.discountPrice === 'number' && p.discountPrice > 0
  ) || [];

  // extract categories from currentSupplier->products
  const supplierCategories = useMemo(() => {
    if (!currentSupplier) return ['All'];

    let products = currentSupplier?.products || [];

    if (products.length === 0) return ['All'];
    const allCategories = products.map(p => p.category);
    return ['All', ...new Set(allCategories)];
  }, [currentSupplier?.products]);

  const filteredSuppliers = suppliers
    .filter(s => s.name.toLowerCase().includes(search.toLowerCase()));

  return ((title==="CustomerProfile") ? (
      <CustomerProfileGUI title={title} />
    ) : (title==="SuppliersMap") ? (
      <SuppliersMap />
    ) : (title==="CustomerOrders") ? (
      <CustomerOrdersGUI />
    ) : (title==="CustomerPackages") ? (
      <CustomerPackagesGUI />
    ) : (title==="OrderDetails") ? (
      <OrderDetails />
    ) : (title==="OrderTracking") ? (
      <OrderTracking />
    ) : (title==="CustomerHome") ? (
      <CustomerHomeGUI />
    ) : (title==="SupplierCategories") ? (
      <SupplierCategoriesGUI />
    ) : (title==="SuppliersPage") ? (
      <SuppliersPageGUI 
        category={category} 
        search={search} 
        setSearch={setSearch} 
        filteredSuppliers={filteredSuppliers} 
        filteredPromotions={filteredPromotions} 
      />
    ) : (title==="SupplierDetails") ? (
      currentSupplier ? (
        <SupplierDetailsGUI
          currentSupplier={currentSupplier}
          supplierPromotions={supplierPromotions ?? []}
          setShowFilters={setShowFilters}
          supplierCategories={supplierCategories}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          filteredProducts={filteredProducts}
        />
      ) : (
        <YStack
          flex={1}
          justifyContent="center"
          alignItems="center"
          gap="$4"
          padding="$4"
        >
          {loading ? (
            <>
              <Text fontSize="$5">Loading supplier details...</Text>
              <ActivityIndicator size="large" color="#3498db" />
            </>
          ) : (
            <Text fontSize="$5" color="$red10">Supplier not found</Text>
          )}
        </YStack>
      )
    ) : (title==="SupplierProductDetails") ? (
      <SupplierProductDetailsGUI 
        category={category || ''}
      />
    ) : (title==="OrderCheckout") ? (
      <CheckoutPage category={category || ''}/>
    ) : (title==="OrderConfirmation") ? (
      <OrderConfirmation />
    ) : (title==="SendPackage") ? (
      <SendPackageForm />
    ) : (title==="SendPackageConfirmation") ? (
      <SendPackageConfirmation />
      
    ) : (title==="RequestPickup") ? (
      <RequestPickupForm />
      
    ) : (title==="RequestPickupConfirmation") ? (
      <RequestPickupConfirmation />
      
    ) : (title==="SelectLocation") ? (
      <SelectLocation />
    ) : (title==="AIChat") ? (
      <AIChatGUI />
    ) : (title==="SupplierProductDetails") ? (
      <SupplierProductDetailsGUI category={category || ''} />
    ) : (
      <PackageTracking />
    )
  );
};