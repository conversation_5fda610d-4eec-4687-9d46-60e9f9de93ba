import { Card, YStack, Text, H2, XStack, Paragraph, Input, Image, Button, View } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, ScrollView, StyleSheet, Dimensions } from 'react-native';
import { Stack } from 'expo-router';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { MotiView } from 'moti';

const { width } = Dimensions.get('window');

type SuppliersPageGUIProps = {
    category: string;
    search: string;
    setSearch: (value: string) => void;
    filteredSuppliers: Array<{
        id: string;
        name: string;
        logoUrl: string;
        rating: number;
        tags: string[];
    }>;
    filteredPromotions: Array<{
        id: string;
        title: string;
    }>;
};

export const SuppliersPageGUI = ({ category, search, setSearch, filteredSuppliers, filteredPromotions }: SuppliersPageGUIProps) => {
    const { t } = useTranslation();
    const router = useRouter();
    return (
        <>
            <Stack.Screen options={{ title: category ? t(`categories.${category}`, { defaultValue: formatTitle(category) }) : t('suppliers.suppliers', { defaultValue: 'Suppliers' }), headerShown: true }} />
            <ScrollView contentContainerStyle={{ padding: 16, paddingBottom: 40 }}>
            <YStack gap="$4">

            {/* Header */}
            <MotiView
                from={{ opacity: 0, translateY: -20 }}
                animate={{ opacity: 1, translateY: 0 }}
                transition={{ type: 'timing', duration: 500 }}
            >
                <YStack gap="$2" mb="$2">
                    <H2 color="$gray12" fontSize="$8" fontWeight="800">
                        {category ? t(`categories.${category}`, { defaultValue: formatTitle(category) }) : t('suppliers.suppliers', { defaultValue: 'Suppliers' })}
                    </H2>
                    <Paragraph color="$gray10" fontSize="$4" lineHeight="$1">
                        {t('categories.findBest', {
                          category: category ? t(`categories.${category}`, { defaultValue: formatTitle(category) }).toLowerCase() : t('suppliers.suppliers', { defaultValue: 'suppliers' }).toLowerCase(),
                          defaultValue: `Find the best ${category ? formatTitle(category).toLowerCase() : 'suppliers'} near you`
                        })}
                    </Paragraph>
                </YStack>
            </MotiView>

            {/* Search + Filter Row */}
            <MotiView
                from={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ type: 'timing', duration: 400, delay: 200 }}
            >
                <XStack gap="$3" ai="center" jc="space-between" width="100%">
                    <View style={{
                        flex: 1,
                        position: 'relative',
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.05,
                        shadowRadius: 8,
                        elevation: 2
                    }}>
                        <Input
                        flex={1}
                        placeholder={t('suppliers.searchPlaceholder', {
                          category: category ? t(`categories.${category}`, { defaultValue: formatTitle(category) }).toLowerCase() : t('suppliers.suppliers', { defaultValue: 'suppliers' }).toLowerCase(),
                          defaultValue: `Search ${category ? formatTitle(category).toLowerCase() : 'suppliers'}...`
                        })}
                        value={search}
                        onChangeText={setSearch}
                        size="$5"
                        br="$6"
                        bw="$0"
                        bg="$background"
                        pl="$5"
                        pr="$4"
                        fontSize="$4"
                        placeholderTextColor="$gray9"
                        />
                        <View style={{
                            position: 'absolute',
                            left: 16,
                            top: '50%',
                            transform: [{ translateY: -10 }]
                        }}>
                            <Ionicons name="search" size={20} color="#9CA3AF" />
                        </View>
                    </View>
                    <Button
                        size="$5"
                        br="$6"
                        bg="$blue6"
                        color="white"
                        fontWeight="600"
                        px="$4"
                        elevation="$2"
                        shadowColor="$blue8"
                        shadowOffset={{ width: 0, height: 4 }}
                        shadowOpacity={0.2}
                        shadowRadius={8}
                        hoverStyle={{ bg: '$blue7', scale: 1.05 }}
                        pressStyle={{ bg: '$blue8', scale: 0.98 }}
                        icon={<Ionicons name="filter-outline" size={20} color="white" />}
                    >
                        {t('categories.filter', { defaultValue: 'Filter' })}
                    </Button>
                </XStack>
            </MotiView>

            {/* Promotions Section */}
            <YStack gap="$2" width="100%">
                <Text fontWeight="bold" fontSize="$5">🔥 {t('categories.promotions', { defaultValue: 'Promotions' })}</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <XStack gap="$3">
                    {filteredPromotions.map((promotion) => 
                        <PromoCard key={promotion.id} title={promotion.title} />
                    )}
                </XStack>
                </ScrollView>
            </YStack>

            {/* Supplier List */}
            <YStack gap="$4">
                <Text fontWeight="bold" fontSize="$6" color="$gray12">
                    {t('suppliers.availableSuppliers', { defaultValue: 'Available Suppliers' })}
                </Text>
                {filteredSuppliers.map((supplier, index) => (
                <MotiView
                    key={supplier.id}
                    from={{ opacity: 0, translateY: 30 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{
                        type: 'timing',
                        duration: 400,
                        delay: index * 100
                    }}
                >
                    <Pressable
                        onPress={() => router.push({
                        pathname: "/home/<USER>",
                        params: { supplierId: supplier.id }
                        })}
                        style={({ pressed }) => ({
                            transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                        })}
                    >
                        <Card
                        pointerEvents='none'
                        p="$4"
                        br="$6"
                        bw="$0"
                        bg="$background"
                        elevation="$4"
                        shadowColor="$shadowColor"
                        shadowOffset={{ width: 0, height: 4 }}
                        shadowOpacity={0.08}
                        shadowRadius={12}
                        animation="quick"
                        hoverStyle={{
                            scale: 1.02,
                            shadowOpacity: 0.15,
                            shadowRadius: 16
                        }}
                        pressStyle={{ scale: 0.98 }}
                        >
                        <XStack ai="center" gap="$4" jc="space-between">
                            <XStack ai="center" gap="$4" flex={1}>
                                <View style={{
                                    borderRadius: 16,
                                    overflow: 'hidden',
                                    elevation: 2,
                                    shadowColor: '#000',
                                    shadowOffset: { width: 0, height: 2 },
                                    shadowOpacity: 0.1,
                                    shadowRadius: 4
                                }}>
                                    <Image
                                    source={{ uri: supplier.logoUrl }}
                                    width={70}
                                    height={70}
                                    borderRadius={16}
                                    />
                                </View>
                                <YStack flex={1} gap="$2">
                                    <Text fontWeight="bold" fontSize="$6" color="$gray12">
                                        {supplier.name}
                                    </Text>
                                    <XStack ai="center" gap="$2">
                                        <XStack ai="center" gap="$1" bg="$yellow2" px="$2" py="$1" br="$3">
                                            <Ionicons name="star" size={14} color="#FFB800" />
                                            <Text fontSize="$3" fontWeight="600" color="$yellow11">
                                                {supplier.rating}
                                            </Text>
                                        </XStack>
                                        <Text fontSize="$3" color="$gray10">
                                            • {supplier.tags.slice(0, 2).join(', ')}
                                        </Text>
                                    </XStack>
                                    <XStack ai="center" gap="$2">
                                        <Ionicons name="time-outline" size={14} color="#666" />
                                        <Text fontSize="$2" color="$gray9">
                                            30-45 mins
                                        </Text>
                                        <Ionicons name="location-outline" size={14} color="#666" />
                                        <Text fontSize="$2" color="$gray9">
                                            2.5 km
                                        </Text>
                                    </XStack>
                                </YStack>
                            </XStack>
                            <View style={{
                                backgroundColor: '#F0F9FF',
                                borderRadius: 12,
                                padding: 8
                            }}>
                                <Ionicons name="chevron-forward" size={20} color="#0EA5E9" />
                            </View>
                        </XStack>
                        </Card>
                    </Pressable>
                </MotiView>
                ))}
            </YStack>
            </YStack>
        </ScrollView>
      </>
    );
};

function formatTitle(slug: string | undefined) {
  if (!slug) return ''
  return slug.charAt(0).toUpperCase() + slug.slice(1).replace('-', ' ')
}

function PromoCard({ title }: { title: string }) {
  return (
    <MotiView
      from={{ opacity: 0, scale: 0.9, translateY: 20 }}
      animate={{ opacity: 1, scale: 1, translateY: 0 }}
      transition={{ type: 'timing', duration: 400 }}
    >
      <Card
        w={280}
        h={120}
        p="$0"
        br="$6"
        bw="$0"
        elevation="$6"
        shadowColor="$shadowColor"
        shadowOffset={{ width: 0, height: 8 }}
        shadowOpacity={0.15}
        shadowRadius={16}
        animation="quick"
        hoverStyle={{ scale: 1.02, shadowOpacity: 0.25 }}
        pressStyle={{ scale: 0.98 }}
        overflow="hidden"
      >
        <LinearGradient
          colors={['#FF6B6B', '#FF8E53', '#FFB74D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={StyleSheet.absoluteFillObject}
        />
        <View style={{ position: 'relative', zIndex: 1, padding: 16, flex: 1, justifyContent: 'space-between' }}>
          <XStack ai="center" gap="$2">
            <View style={{
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderRadius: 12,
              padding: 6
            }}>
              <Ionicons name="flash" size={16} color="white" />
            </View>
            <Text color="white" fontWeight="600" fontSize="$2" opacity={0.9}>
              LIMITED OFFER
            </Text>
          </XStack>
          <YStack gap="$1">
            <Text color="white" fontWeight="bold" fontSize="$5" lineHeight="$1">
              {title}
            </Text>
            <Text color="white" fontSize="$3" opacity={0.8}>
              Don't miss out!
            </Text>
          </YStack>
        </View>
      </Card>
    </MotiView>
  )
}