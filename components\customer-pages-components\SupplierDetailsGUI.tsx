import { Card, YStack, Text, XStack, Paragraph, Input, Image, Button, Separator, View, H1, H2 } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, ScrollView, StyleSheet, Dimensions } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { Sheet } from 'tamagui';
import { CartPage } from './Cart';
import { useCartStore } from './CartStore';
import { useTranslation } from 'react-i18next';
import { TamaguiProvider } from 'tamagui';
import { PortalProvider } from '@tamagui/portal';
import { LinearGradient } from 'expo-linear-gradient';
import { MotiView } from 'moti';

const { width, height } = Dimensions.get('window');

// Responsive breakpoints
const isTablet = width >= 768;
const isDesktop = width >= 1024;

// Responsive values
const getResponsiveValue = (mobile: number, tablet: number, desktop: number) => {
  if (isDesktop) return desktop;
  if (isTablet) return tablet;
  return mobile;
};

const getResponsivePadding = () => {
  if (isDesktop) return 32;
  if (isTablet) return 24;
  return 16;
};

const getResponsiveColumns = () => {
  if (isDesktop) return 3;
  if (isTablet) return 2;
  return 1;
};


type Addition = { id: string; name: string; price: number }

type SupplierDetailsGUIProps = {
    currentSupplier: {
        id: string;
        name: string;
        banner: string;
        logoUrl: string;
        rating: number;
        deliveryTime: string;
        openHours: string;
        tags: string[];
        phone: string;
        category: string;
        products: Array<{
            id: string;
            name: string;
            image: string;
            price: number;
            category: string;
            restaurantOptions?: {
                additions?: Addition[]
                without?: string[]
                sides?: Addition[]
                /* options can be changed for each meal - resturant put sections it want */
            }
            clothingOptions?: {
                sizes: string[]
                colors: string[]
                gallery: string[]
            }
        }>;
    } | null;
    supplierPromotions: Array<{
        id: string;
        name: string;
        image: string;
        price: number;
        discountPrice: number;
        category: string;
    }>;
    setShowFilters: (show: boolean) => void;
    supplierCategories: string[];
    selectedCategory: string;
    setSelectedCategory: (category: string) => void;
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    filteredProducts: Array<{
            id: string;
            name: string;
            image: string;
            price: number;
            category: string;
            restaurantOptions?: {
                additions?: Addition[]
                without?: string[]
                sides?: Addition[]
                /* options can be changed for each meal - resturant put sections it want */ 
            }
            clothingOptions?: {
                sizes: string[]
                colors: string[]
                gallery: string[]
            }
    }>;
};

export const SupplierDetailsGUI = ({ currentSupplier, supplierPromotions, setShowFilters, supplierCategories, selectedCategory, setSelectedCategory, searchQuery, setSearchQuery, filteredProducts }: SupplierDetailsGUIProps) => {
    const { t } = useTranslation();
    const { addItem, removeItem, updateQty } = useCartStore();

    // Add null check for currentSupplier
    if (!currentSupplier) {
        return (
          <PortalProvider>
            <YStack flex={1} justifyContent="center" alignItems="center" gap="$4" padding="$4">
                <Text fontSize="$5" color="$red10">Supplier data not available</Text>
            </YStack>
            </PortalProvider>
        );
    }

    const items = useCartStore(state => state.items.filter(i => i.supplierCategory === currentSupplier.category));
    const totalQty = items.reduce((sum, i) => sum + i.qty, 0);
    const totalPrice = items.reduce((sum, i) => sum + i.qty * i.finalPrice, 0);

    const router = useRouter();

    const [open, setOpen] = useState(false);///

    return (
        <>
            <Stack.Screen options={{ title: currentSupplier?.name || 'Supplier Details', headerShown: true }} />

            <ScrollView contentContainerStyle={{
                paddingBottom: 120,
                paddingHorizontal: getResponsivePadding(),
                maxWidth: isDesktop ? 1200 : '100%',
                alignSelf: 'center',
                width: '100%'
            }}>
                <YStack gap={getResponsiveValue(16, 20, 24)} p={getResponsiveValue(16, 20, 24)}>
                
                {/* Enhanced Banner */}
                <MotiView
                    from={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ type: 'timing', duration: 600 }}
                >
                    <Card
                        p="$0"
                        br="$6"
                        bw="$0"
                        elevation="$6"
                        shadowColor="$shadowColor"
                        shadowOffset={{ width: 0, height: 8 }}
                        shadowOpacity={0.15}
                        shadowRadius={16}
                        overflow="hidden"
                        height={getResponsiveValue(220, 280, 320)}
                        position="relative"
                    >
                        <Image
                            source={{ uri: currentSupplier.banner || '' }}
                            width="100%"
                            height="100%"
                            style={StyleSheet.absoluteFillObject}
                        />
                        <LinearGradient
                            colors={['transparent', 'rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
                            style={StyleSheet.absoluteFillObject}
                        />
                        <View style={{
                            position: 'absolute',
                            bottom: getResponsiveValue(16, 24, 32),
                            left: getResponsiveValue(16, 24, 32),
                            right: getResponsiveValue(16, 24, 32),
                            zIndex: 1
                        }}>
                            <XStack ai="flex-end" jc="space-between" gap="$4">
                                <YStack gap={getResponsiveValue(8, 12, 16)} flex={1}>
                                    <H1
                                        color="white"
                                        fontSize={getResponsiveValue(28, 36, 44)}
                                        fontWeight="800"
                                        lineHeight="$1"
                                        numberOfLines={isTablet ? 2 : 1}
                                    >
                                        {currentSupplier.name || 'Unknown Supplier'}
                                    </H1>
                                    <XStack gap={getResponsiveValue(12, 16, 20)} ai="center" flexWrap="wrap">
                                        <XStack ai="center" gap="$1" bg="rgba(255,255,255,0.2)" px="$3" py="$2" br="$4">
                                            <Ionicons name="star" size={getResponsiveValue(16, 18, 20)} color="#FFD700" />
                                            <Text color="white" fontWeight="600" fontSize={getResponsiveValue(14, 16, 18)}>
                                                {currentSupplier.rating || 0}
                                            </Text>
                                        </XStack>
                                        <XStack ai="center" gap="$1" bg="rgba(255,255,255,0.2)" px="$3" py="$2" br="$4">
                                            <Ionicons name="time-outline" size={getResponsiveValue(16, 18, 20)} color="white" />
                                            <Text color="white" fontWeight="600" fontSize={getResponsiveValue(14, 16, 18)}>
                                                {currentSupplier.deliveryTime || '30-45 min'}
                                            </Text>
                                        </XStack>
                                    </XStack>
                                </YStack>
                                <View style={{
                                    borderRadius: getResponsiveValue(20, 24, 28),
                                    overflow: 'hidden',
                                    borderWidth: getResponsiveValue(3, 4, 5),
                                    borderColor: 'white',
                                    elevation: 4,
                                    shadowColor: '#000',
                                    shadowOffset: { width: 0, height: 2 },
                                    shadowOpacity: 0.3,
                                    shadowRadius: 4
                                }}>
                                    <Image
                                        source={{ uri: currentSupplier.logoUrl || '' }}
                                        width={getResponsiveValue(80, 100, 120)}
                                        height={getResponsiveValue(80, 100, 120)}
                                    />
                                </View>
                            </XStack>
                        </View>
                    </Card>
                </MotiView>

                {/* Enhanced Info Section */}
                <MotiView
                    from={{ opacity: 0, translateY: 20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 500, delay: 200 }}
                >
                    <Card
                        p="$4"
                        br="$5"
                        bw="$0"
                        bg="$background"
                        elevation="$3"
                        shadowColor="$shadowColor"
                        shadowOffset={{ width: 0, height: 4 }}
                        shadowOpacity={0.08}
                        shadowRadius={12}
                    >
                        <YStack gap="$3">
                            <XStack ai="center" gap="$2">
                                <Ionicons name="time-outline" size={20} color="#10B981" />
                                <Text fontSize="$4" fontWeight="600" color="$gray12">
                                    {t('suppliers.openHours', { defaultValue: 'Open' })}:
                                </Text>
                                <Text fontSize="$4" color="$green10" fontWeight="600">
                                    {currentSupplier.openHours || '10:00 AM - 11:00 PM'}
                                </Text>
                            </XStack>

                            <XStack ai="center" gap="$2" flexWrap="wrap">
                                {(currentSupplier.tags || ['Fast Food', 'Shawarma']).map((tag, index) => (
                                    <View key={index} style={{
                                        backgroundColor: '#F3F4F6',
                                        borderRadius: 16,
                                        paddingHorizontal: 12,
                                        paddingVertical: 6
                                    }}>
                                        <Text fontSize="$3" color="$gray11" fontWeight="500">
                                            {tag}
                                        </Text>
                                    </View>
                                ))}
                            </XStack>

                            <XStack ai="center" gap="$2">
                                <Ionicons name="call-outline" size={20} color="#3B82F6" />
                                <Text fontSize="$4" color="$gray10">
                                    Contact:
                                </Text>
                                <Text fontSize="$4" color="$blue10" fontWeight="600">
                                    {currentSupplier.phone || '+970593456789'}
                                </Text>
                            </XStack>
                        </YStack>
                    </Card>
                </MotiView>

                {/* Enhanced Contact Button */}
                <MotiView
                    from={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ type: 'timing', duration: 400, delay: 300 }}
                >
                    <Button
                        size="$5"
                        br="$6"
                        bg="$green6"
                        color="white"
                        fontWeight="700"
                        fontSize="$5"
                        elevation="$4"
                        shadowColor="$green8"
                        shadowOffset={{ width: 0, height: 4 }}
                        shadowOpacity={0.3}
                        shadowRadius={8}
                        hoverStyle={{ bg: '$green7', scale: 1.02 }}
                        pressStyle={{ bg: '$green8', scale: 0.98 }}
                        icon={<Ionicons name="chatbubble-ellipses" size={20} color="white" />}
                        onPress={() => console.log('Chat with supplier')}
                    >
                        {t('suppliers.chatWithSupplier', { defaultValue: 'Chat with Supplier' })}
                    </Button>
                </MotiView>

                {/* Enhanced Menu Section */}
                <MotiView
                    from={{ opacity: 0, translateY: 20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 500, delay: 400 }}
                >
                    <YStack gap="$4" mt="$4">
                        <XStack ai="center" gap="$3">
                            <View style={{
                                backgroundColor: '#FEF3C7',
                                borderRadius: 12,
                                padding: 8
                            }}>
                                <Ionicons
                                    name={currentSupplier.category === "restaurants" ? "restaurant" : "storefront"}
                                    size={24}
                                    color="#F59E0B"
                                />
                            </View>
                            <YStack>
                                <H2 fontSize="$7" fontWeight="800" color="$gray12">
                                    {(currentSupplier.category==="restaurants") ? t('suppliers.menu', { defaultValue: 'Menu' }) : t('suppliers.products', { defaultValue: 'Products' })}
                                </H2>
                                <Text fontSize="$3" color="$gray10">
                                    Discover our delicious offerings
                                </Text>
                            </YStack>
                        </XStack>

                        {/* Enhanced Search & Filter */}
                        <XStack gap="$3" ai="center" jc="space-between">
                            <View style={{
                                flex: 1,
                                position: 'relative',
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 2 },
                                shadowOpacity: 0.05,
                                shadowRadius: 8,
                                elevation: 2
                            }}>
                                <Input
                                    placeholder={t('common.search', { defaultValue: 'Search menu items...' })}
                                    flex={1}
                                    value={searchQuery}
                                    onChangeText={setSearchQuery}
                                    size="$5"
                                    br="$6"
                                    bw="$0"
                                    bg="$background"
                                    pl="$5"
                                    pr="$4"
                                    fontSize="$4"
                                    placeholderTextColor="$gray9"
                                />
                                <View style={{
                                    position: 'absolute',
                                    left: 16,
                                    top: '50%',
                                    transform: [{ translateY: -10 }]
                                }}>
                                    <Ionicons name="search" size={20} color="#9CA3AF" />
                                </View>
                            </View>
                            <Button
                                size="$5"
                                br="$6"
                                bg="$blue6"
                                color="white"
                                px="$3"
                                elevation="$2"
                                hoverStyle={{ bg: '$blue7' }}
                                pressStyle={{ bg: '$blue8' }}
                                icon={<Ionicons name="filter-outline" size={18} color="white" />}
                                onPress={() => setShowFilters(true)}
                            />
                            <Button
                                size="$5"
                                br="$6"
                                bg="$purple6"
                                color="white"
                                px="$3"
                                elevation="$2"
                                hoverStyle={{ bg: '$purple7' }}
                                pressStyle={{ bg: '$purple8' }}
                                icon={<Ionicons name="swap-vertical-outline" size={18} color="white" />}
                                onPress={() => console.log('Sort by price')}
                            />
                        </XStack>
                    </YStack>
                </MotiView>

                {/* Enhanced Promotions */}
                <MotiView
                    from={{ opacity: 0, translateX: -30 }}
                    animate={{ opacity: 1, translateX: 0 }}
                    transition={{ type: 'timing', duration: 500, delay: 500 }}
                >
                    <YStack gap="$3" mt="$4">
                        <XStack ai="center" gap="$3">
                            <View style={{
                                backgroundColor: '#FEE2E2',
                                borderRadius: 12,
                                padding: 8
                            }}>
                                <Ionicons name="pricetag" size={20} color="#EF4444" />
                            </View>
                            <YStack>
                                <Text fontSize="$6" fontWeight="bold" color="$gray12">
                                    {t('categories.promotions', { defaultValue: 'Special Offers' })}
                                </Text>
                                <Text fontSize="$3" color="$gray10">
                                    Limited time deals
                                </Text>
                            </YStack>
                        </XStack>
                        {isTablet ? (
                            // Grid layout for tablets and desktop
                            <View style={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                gap: getResponsiveValue(16, 20, 24),
                                justifyContent: 'space-between'
                            }}>
                                {Array.isArray(supplierPromotions) && supplierPromotions.map((item, index) => (
                                    <MotiView
                                        key={item.id}
                                        from={{ opacity: 0, scale: 0.9 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ type: 'timing', duration: 300, delay: index * 100 }}
                                        style={{
                                            width: isDesktop ? '30%' : '48%',
                                            minWidth: 200
                                        }}
                                    >
                                        <Card
                                            width="100%"
                                            p="$0"
                                            br="$6"
                                            bw="$0"
                                            elevation="$4"
                                            shadowColor="$shadowColor"
                                            shadowOffset={{ width: 0, height: 4 }}
                                            shadowOpacity={0.1}
                                            shadowRadius={8}
                                            overflow="hidden"
                                            hoverStyle={{ scale: 1.02 }}
                                            pressStyle={{ scale: 0.98 }}
                                        >
                        ) : (
                            // Horizontal scroll for mobile
                            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                                <XStack gap="$4" px="$2">
                                {Array.isArray(supplierPromotions) && supplierPromotions.map((item, index) => (
                                    <MotiView
                                        key={item.id}
                                        from={{ opacity: 0, scale: 0.9 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ type: 'timing', duration: 300, delay: index * 100 }}
                                    >
                                        <Card
                                            width={getResponsiveValue(220, 260, 300)}
                                            p="$0"
                                            br="$6"
                                            bw="$0"
                                            elevation="$4"
                                            shadowColor="$shadowColor"
                                            shadowOffset={{ width: 0, height: 4 }}
                                            shadowOpacity={0.1}
                                            shadowRadius={8}
                                            overflow="hidden"
                                            hoverStyle={{ scale: 1.02 }}
                                            pressStyle={{ scale: 0.98 }}
                                        >
                                        <Image source={{ uri: item.image }} height={120} width="100%" />
                                        <YStack p="$3" gap="$2">
                                            <Text fontWeight="bold" fontSize="$4" color="$gray12">
                                                {item.name}
                                            </Text>
                                            <XStack ai="center" gap="$2">
                                                <View style={{
                                                    backgroundColor: '#FEE2E2',
                                                    borderRadius: 8,
                                                    paddingHorizontal: 8,
                                                    paddingVertical: 4
                                                }}>
                                                    <Text color="$red10" fontSize="$2" fontWeight="600">
                                                        -{item.discountPrice}%
                                                    </Text>
                                                </View>
                                                <Text color="$red10" fontSize="$4" fontWeight="bold">
                                                    ₪{item.price}
                                                </Text>
                                            </XStack>
                                        </YStack>
                                    </Card>
                                </MotiView>
                            ))}
                                </XStack>
                            </ScrollView>
                        )}
                    </YStack>
                </MotiView>

                {/* Enhanced Category Tabs */}
                <MotiView
                    from={{ opacity: 0, translateY: 20 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 400, delay: 600 }}
                >
                    <YStack gap="$3" mt="$5">
                        <Text fontSize="$5" fontWeight="bold" color="$gray12">
                            Categories
                        </Text>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                            <XStack gap="$3" px="$2">
                                {supplierCategories.map((cat, index) => (
                                <MotiView
                                    key={cat}
                                    from={{ opacity: 0, scale: 0.9 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ type: 'timing', duration: 300, delay: index * 50 }}
                                >
                                    <Button
                                        size="$4"
                                        br="$6"
                                        px="$4"
                                        py="$2"
                                        bg={selectedCategory === cat ? '$blue6' : '$gray3'}
                                        color={selectedCategory === cat ? 'white' : '$gray11'}
                                        fontWeight="600"
                                        elevation={selectedCategory === cat ? '$3' : '$1'}
                                        shadowColor={selectedCategory === cat ? '$blue8' : '$gray6'}
                                        shadowOffset={{ width: 0, height: 2 }}
                                        shadowOpacity={selectedCategory === cat ? 0.3 : 0.1}
                                        shadowRadius={selectedCategory === cat ? 6 : 3}
                                        hoverStyle={{
                                            scale: 1.05,
                                            bg: selectedCategory === cat ? '$blue7' : '$gray4'
                                        }}
                                        pressStyle={{
                                            scale: 0.95,
                                            bg: selectedCategory === cat ? '$blue8' : '$gray5'
                                        }}
                                        onPress={() => setSelectedCategory(cat)}
                                    >
                                        {cat}
                                    </Button>
                                </MotiView>
                                ))}
                            </XStack>
                        </ScrollView>
                    </YStack>
                </MotiView>

                {/* Enhanced Product List */}
                <MotiView
                    from={{ opacity: 0, translateY: 30 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ type: 'timing', duration: 500, delay: 700 }}
                >
                    <YStack gap={getResponsiveValue(16, 20, 24)} mt="$4">
                        <Text fontSize={getResponsiveValue(20, 24, 28)} fontWeight="bold" color="$gray12">
                            Menu Items
                        </Text>
                        <View style={{
                            flexDirection: isTablet ? 'row' : 'column',
                            flexWrap: isTablet ? 'wrap' : 'nowrap',
                            gap: getResponsiveValue(16, 20, 24),
                            justifyContent: isTablet ? 'space-between' : 'flex-start'
                        }}>
                        {filteredProducts.map((product, index) => (
                        <MotiView
                            key={product.id}
                            from={{ opacity: 0, translateX: -20 }}
                            animate={{ opacity: 1, translateX: 0 }}
                            transition={{ type: 'timing', duration: 400, delay: index * 100 }}
                            style={{
                                width: isTablet ? (isDesktop ? '32%' : '48%') : '100%',
                                minWidth: isTablet ? 300 : 'auto'
                            }}
                        >
                            <Pressable
                                onPress={() => router.push({
                                    pathname: "/home/<USER>",
                                    params: {productId: product.id, supplierId: currentSupplier.id, supplierName: currentSupplier.name, category: currentSupplier.category, product: JSON.stringify(product)}
                                })}
                                style={({ pressed }) => ({
                                    transform: pressed ? [{ scale: 0.98 }] : [{ scale: 1 }]
                                })}
                                disabled={!((currentSupplier.category === 'restaurants') || (currentSupplier.category === 'clothings'))}
                            >
                            <Card
                                p="$4"
                                br="$6"
                                bw="$0"
                                bg="$background"
                                elevation="$3"
                                shadowColor="$shadowColor"
                                shadowOffset={{ width: 0, height: 4 }}
                                shadowOpacity={0.08}
                                shadowRadius={12}
                                hoverStyle={{
                                    scale: 1.02,
                                    shadowOpacity: 0.15,
                                    shadowRadius: 16
                                }}
                                pressStyle={{ scale: 0.98 }}
                            >
                                <XStack gap="$4" ai="center" jc="space-between">
                                    <XStack gap="$4" ai="center" flex={1}>
                                        <View style={{
                                            borderRadius: 16,
                                            overflow: 'hidden',
                                            elevation: 2,
                                            shadowColor: '#000',
                                            shadowOffset: { width: 0, height: 2 },
                                            shadowOpacity: 0.1,
                                            shadowRadius: 4
                                        }}>
                                            <Image
                                                source={{ uri: product.image }}
                                                width={80}
                                                height={80}
                                            />
                                        </View>
                                        <YStack flex={1} gap="$2">
                                            <Text fontSize="$5" fontWeight="bold" color="$gray12">
                                                {product.name}
                                            </Text>
                                            <Text fontSize="$3" color="$gray10" numberOfLines={2}>
                                                Delicious and freshly prepared with premium ingredients
                                            </Text>
                                            <XStack ai="center" gap="$2">
                                                <Text fontSize="$5" fontWeight="bold" color="$green10">
                                                    ₪{product.price}
                                                </Text>
                                                <View style={{
                                                    backgroundColor: '#FEF3C7',
                                                    borderRadius: 8,
                                                    paddingHorizontal: 8,
                                                    paddingVertical: 2
                                                }}>
                                                    <Text fontSize="$2" color="$yellow11" fontWeight="600">
                                                        Popular
                                                    </Text>
                                                </View>
                                            </XStack>
                                        </YStack>
                                    </XStack>
                        {(() => {
                            if(!(currentSupplier.category == "restaurants" || currentSupplier.category == "clothings")){
                                const inCartQty = useCartStore(state =>
                                    state.items.find(i => i.product.id === product.id && i.supplierCategory === currentSupplier.category)?.qty ?? 0
                                );

                                return inCartQty === 0 ? (
                                <Button
                                    size="$2"
                                    icon={<Ionicons name="add" size={16} />}
                                    onPress={() => {
                                        const newItem = {
                                            id: product.id,
                                            name: product.name,
                                            price: product.price,
                                            image: product.image,
                                            category: product.category
                                        };
                                        addItem({
                                            product: newItem,
                                            qty: 1,
                                            finalPrice: newItem.price,
                                            supplierId: currentSupplier.id,
                                            supplierName: currentSupplier.name,
                                            supplierCategory: currentSupplier.category
                                        });
                                    }}
                                />
                                ) : (
                                <XStack ai="center" gap="$2" width={"25%"}>
                                    <Button
                                    icon={<Ionicons name="remove" size={16} />}
                                    size="$2"
                                    onPress={() => {
                                        const item = items.find(i => i.product.id === product.id && i.supplierCategory === currentSupplier.category);
                                        if (item) {
                                            if (item.qty > 1) {
                                                updateQty(item.id, item.qty - 1);
                                            } else {
                                                removeItem(item.id);
                                            }
                                        }
                                    }}
                                    />
                                    <Text>{inCartQty}</Text>
                                    <Button
                                    icon={<Ionicons name="add" size={16} />}
                                    size="$2"
                                    onPress={() => {
                                        const newItem = {
                                            id: product.id,
                                            name: product.name,
                                            price: product.price,
                                            image: product.image,
                                            category: product.category
                                        };
                                        addItem({
                                            product: newItem,
                                            qty: inCartQty,
                                            finalPrice: inCartQty * newItem.price,
                                            supplierId: currentSupplier.id,
                                            supplierName: currentSupplier.name,
                                            supplierCategory: currentSupplier.category
                                        });
                                    }}
                                    />
                                </XStack>
                                )
                            }
                        })()}
                        </XStack>
                    </Card>
                    </Pressable>
                        </MotiView>
                    ))}
                        </View>
                    </YStack>
                </MotiView>


                </YStack>
            </ScrollView>

            {/* Enhanced Floating Cart */}
            {totalQty > 0 && (
                <MotiView
                    from={{ opacity: 0, translateY: 100 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    exit={{ opacity: 0, translateY: 100 }}
                    transition={{ type: 'spring', damping: 20, stiffness: 300 }}
                    style={{
                        position: 'absolute',
                        bottom: getResponsiveValue(20, 24, 32),
                        left: getResponsiveValue(16, 24, 32),
                        right: getResponsiveValue(16, 24, 32),
                        zIndex: 1000,
                        maxWidth: isDesktop ? 600 : '100%',
                        alignSelf: 'center'
                    }}
                >
                    <Card
                        p="$0"
                        br="$8"
                        bw="$0"
                        elevation="$8"
                        shadowColor="$shadowColor"
                        shadowOffset={{ width: 0, height: 12 }}
                        shadowOpacity={0.25}
                        shadowRadius={20}
                        overflow="hidden"
                    >
                        <LinearGradient
                            colors={['#667eea', '#764ba2']}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                            style={{ padding: 20 }}
                        >
                            <XStack jc="space-between" ai="center">
                                <XStack gap="$3" ai="center" flex={1}>
                                    <View style={{
                                        backgroundColor: 'rgba(255,255,255,0.2)',
                                        borderRadius: 12,
                                        padding: 8
                                    }}>
                                        <Ionicons name="cart" size={24} color="white" />
                                    </View>
                                    <YStack>
                                        <Text color="white" fontWeight="bold" fontSize="$5">
                                            {totalQty} {t('suppliers.items', { defaultValue: 'items' })}
                                        </Text>
                                        <Text color="white" fontSize="$6" fontWeight="800">
                                            ₪{totalPrice.toFixed(2)}
                                        </Text>
                                    </YStack>
                                </XStack>
                                <Button
                                    bg="white"
                                    color="$purple10"
                                    fontWeight="700"
                                    fontSize="$4"
                                    br="$6"
                                    px="$5"
                                    py="$3"
                                    elevation="$4"
                                    shadowColor="rgba(0,0,0,0.3)"
                                    shadowOffset={{ width: 0, height: 4 }}
                                    shadowOpacity={0.3}
                                    shadowRadius={8}
                                    hoverStyle={{
                                        scale: 1.05,
                                        shadowOpacity: 0.4
                                    }}
                                    pressStyle={{
                                        scale: 0.95,
                                        shadowOpacity: 0.2
                                    }}
                                    onPress={() => setOpen(true)}
                                >
                                    {t('suppliers.viewCart', { defaultValue: 'View Cart' })}
                                </Button>
                            </XStack>
                        </LinearGradient>
                    </Card>
                </MotiView>
            )}

            {/* Slide-up Sheet Cart */}
            <Sheet
                modal
                open={open}
                onOpenChange={setOpen}
                snapPoints={[100, 100]}
                dismissOnSnapToBottom
                >
                <Sheet.Frame f={1} p="$4" bg="white" pointerEvents="auto">
                    <CartPage
                        category={currentSupplier.category}
                        onClose={() => setOpen(false)}
                    />
                </Sheet.Frame>
                <Sheet.Overlay pointerEvents="auto" />
            </Sheet>
        </>
    );
};
