import { Card, YStack, Text, XStack, Paragraph, Input, Image, Button, Separator } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, ScrollView } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useState } from 'react';
import { Sheet } from 'tamagui';///
import { CartPage } from './Cart';
import { useCartStore } from './CartStore';
import { useTranslation } from 'react-i18next';
import { TamaguiProvider } from 'tamagui';
import { PortalProvider } from '@tamagui/portal';


type Addition = { id: string; name: string; price: number }

type SupplierDetailsGUIProps = {
    currentSupplier: {
        id: string;
        name: string;
        banner: string;
        logoUrl: string;
        rating: number;
        deliveryTime: string;
        openHours: string;
        tags: string[];
        phone: string;
        category: string;
        products: Array<{
            id: string;
            name: string;
            image: string;
            price: number;
            category: string;
            restaurantOptions?: {
                additions?: Addition[]
                without?: string[]
                sides?: Addition[]
                /* options can be changed for each meal - resturant put sections it want */
            }
            clothingOptions?: {
                sizes: string[]
                colors: string[]
                gallery: string[]
            }
        }>;
    } | null;
    supplierPromotions: Array<{
        id: string;
        name: string;
        image: string;
        price: number;
        discountPrice: number;
        category: string;
    }>;
    setShowFilters: (show: boolean) => void;
    supplierCategories: string[];
    selectedCategory: string;
    setSelectedCategory: (category: string) => void;
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    filteredProducts: Array<{
            id: string;
            name: string;
            image: string;
            price: number;
            category: string;
            restaurantOptions?: {
                additions?: Addition[]
                without?: string[]
                sides?: Addition[]
                /* options can be changed for each meal - resturant put sections it want */ 
            }
            clothingOptions?: {
                sizes: string[]
                colors: string[]
                gallery: string[]
            }
    }>;
};

export const SupplierDetailsGUI = ({ currentSupplier, supplierPromotions, setShowFilters, supplierCategories, selectedCategory, setSelectedCategory, searchQuery, setSearchQuery, filteredProducts }: SupplierDetailsGUIProps) => {
    const { t } = useTranslation();
    const { addItem, removeItem, updateQty } = useCartStore();

    // Add null check for currentSupplier
    if (!currentSupplier) {
        return (
          <PortalProvider>
            <YStack flex={1} justifyContent="center" alignItems="center" gap="$4" padding="$4">
                <Text fontSize="$5" color="$red10">Supplier data not available</Text>
            </YStack>
            </PortalProvider>
        );
    }

    const items = useCartStore(state => state.items.filter(i => i.supplierCategory === currentSupplier.category));
    const totalQty = items.reduce((sum, i) => sum + i.qty, 0);
    const totalPrice = items.reduce((sum, i) => sum + i.qty * i.finalPrice, 0);

    const router = useRouter();

    const [open, setOpen] = useState(false);///

    return (
        <>
            <Stack.Screen options={{ title: currentSupplier?.name || 'Supplier Details', headerShown: true }} />

            <ScrollView contentContainerStyle={{ paddingBottom: 120 }}>
                <YStack gap="$4" p="$4">
                
                {/* Banner */}
                <Image source={{ uri: currentSupplier.banner || '' }} width="100%" height={180} borderRadius={12} />

                {/* Info Row */}
                <XStack ai="center" jc="space-between" width="100%">
                    <YStack gap="$1">
                    <Text fontSize="$7" fontWeight="bold">{currentSupplier.name || 'Unknown Supplier'}</Text>
                    <XStack gap="$2" ai="center">
                        <Ionicons name="star" size={16} color="gold" />
                        <Paragraph>{currentSupplier.rating || 0}</Paragraph>
                        <Separator vertical />
                        <Paragraph>{currentSupplier.deliveryTime || 'N/A'}</Paragraph>
                    </XStack>
                    <Paragraph color="$gray9">{t('suppliers.openHours', { defaultValue: 'Open' })}: {currentSupplier.openHours || 'N/A'}</Paragraph>
                    <Paragraph color="$gray10">{(currentSupplier.tags || []).join(' • ')}</Paragraph>
                    <Paragraph color="$gray9">Contact ON: <Paragraph color="$blue11">{currentSupplier.phone || 'N/A'}</Paragraph></Paragraph>
                    </YStack>

                    {/* Logo */}
                    <Image source={{ uri: currentSupplier.logoUrl || '' }} width={64} height={64} borderRadius={32} />
                </XStack>

                {/* Contact */}
                <Button
                    icon={<Ionicons name="chatbubble-outline" size={18} />}
                    onPress={() => /*router.push(`/chat/${currentSupplier.id}`)*/ console.log('Chat with supplier')}
                    theme="active"
                >
                    {t('suppliers.chatWithSupplier', { defaultValue: 'Chat with Supplier' })}
                </Button>

                <Separator />

                {/* Products */}
                <Text fontSize="$6" fontWeight="bold">{(currentSupplier.category==="restaurants") ? t('suppliers.menu', { defaultValue: 'Menu' }) : t('suppliers.products', { defaultValue: 'Products' })}</Text>
                {/* Filter & Search */}
                <XStack gap="$2" ai="center" jc="space-between">
                    <Input
                        placeholder={t('common.search', { defaultValue: 'Search...' })}
                        flex={1}
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                    />
                    <Button icon={<Ionicons name="filter-outline" size={16} />} onPress={() => setShowFilters(true)} />
                    <Button icon={<Ionicons name="swap-vertical-outline" size={16} />} onPress={() => /*sortBy('price')*/ console.log('Sort by price')} />
                </XStack>

                {/* Promotions */}
                <Text fontSize="$5" fontWeight="bold" mt="$3">{t('categories.promotions', { defaultValue: 'Promotions' })}</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    <XStack gap="$3" mt="$2">
                    {Array.isArray(supplierPromotions) && supplierPromotions.map((item) => (
                        <Card key={item.id} width={200} p="$3" br="$4" bw="$0.5" boc="$gray5">
                        <Image source={{ uri: item.image }} height={100} borderRadius={8} />
                        <Text fontWeight="bold">{item.name}</Text>
                        <Text color="$red10">{t('suppliers.discount', { defaultValue: 'Discount' })} {item.discountPrice}%</Text>
                        <Text color="$red10">{t('suppliers.now', { defaultValue: 'Now' })} ₪{item.price}</Text>
                        </Card>
                    ))}
                    </XStack>
                </ScrollView>

                {/* Category Tabs */}
                <XStack gap="$2" mt="$4" fw="wrap">
                    {supplierCategories.map((cat) => (
                    <Button
                        key={cat}
                        size="$2"
                        theme={selectedCategory === cat ? 'active' : undefined}
                        onPress={() => setSelectedCategory(cat)}
                    >
                        {cat}
                    </Button>
                    ))}
                </XStack>

                {/* Product List */}
                <YStack gap="$3" mt="$3">
                    {filteredProducts.map((product) => (
                    <Pressable
                        key={product.id}
                        onPress={() => router.push({
                            pathname: "/home/<USER>",
                            params: {productId: product.id, supplierId: currentSupplier.id, supplierName: currentSupplier.name, category: currentSupplier.category, product: JSON.stringify(product)}
                        })}
                        style={({ pressed, hovered }) => ({
                            transform: pressed ? [{ scale: 0.95 }] : hovered ? [{ scale: 1.02 }] : [{ scale: 1 }]
                        })}
                        disabled={!((currentSupplier.category === 'restaurants') || (currentSupplier.category === 'clothings'))}
                    >
                    <Card key={product.id} p="$3" br="$4" bw="$0.5" boc="$gray5">
                        <XStack gap="$3" ai="center" jc="space-between">
                        <XStack gap="$3" ai="center" width={"70%"}>
                            <Image source={{ uri: product.image }} width={60} height={60} borderRadius={8} />
                            <YStack width={"70%"}>
                            <Text fontSize="$5">{product.name}</Text>
                            <Text color="$gray10">₪{product.price}</Text>
                            </YStack>
                        </XStack>
                        {(() => {
                            if(!(currentSupplier.category == "restaurants" || currentSupplier.category == "clothings")){
                                const inCartQty = useCartStore(state =>
                                    state.items.find(i => i.product.id === product.id && i.supplierCategory === currentSupplier.category)?.qty ?? 0
                                );

                                return inCartQty === 0 ? (
                                <Button
                                    size="$2"
                                    icon={<Ionicons name="add" size={16} />}
                                    onPress={() => {
                                        const newItem = {
                                            id: product.id,
                                            name: product.name,
                                            price: product.price,
                                            image: product.image,
                                            category: product.category
                                        };
                                        addItem({
                                            product: newItem,
                                            qty: 1,
                                            finalPrice: newItem.price,
                                            supplierId: currentSupplier.id,
                                            supplierName: currentSupplier.name,
                                            supplierCategory: currentSupplier.category
                                        });
                                    }}
                                />
                                ) : (
                                <XStack ai="center" gap="$2" width={"25%"}>
                                    <Button
                                    icon={<Ionicons name="remove" size={16} />}
                                    size="$2"
                                    onPress={() => {
                                        const item = items.find(i => i.product.id === product.id && i.supplierCategory === currentSupplier.category);
                                        if (item) {
                                            if (item.qty > 1) {
                                                updateQty(item.id, item.qty - 1);
                                            } else {
                                                removeItem(item.id);
                                            }
                                        }
                                    }}
                                    />
                                    <Text>{inCartQty}</Text>
                                    <Button
                                    icon={<Ionicons name="add" size={16} />}
                                    size="$2"
                                    onPress={() => {
                                        const newItem = {
                                            id: product.id,
                                            name: product.name,
                                            price: product.price,
                                            image: product.image,
                                            category: product.category
                                        };
                                        addItem({
                                            product: newItem,
                                            qty: inCartQty,
                                            finalPrice: inCartQty * newItem.price,
                                            supplierId: currentSupplier.id,
                                            supplierName: currentSupplier.name,
                                            supplierCategory: currentSupplier.category
                                        });
                                    }}
                                    />
                                </XStack>
                                )
                            }
                        })()}
                        </XStack>
                    </Card>
                    </Pressable>
                    ))}
                </YStack>


                </YStack>
            </ScrollView>

            {/* Sticky Cart */}
            <YStack position="absolute" bottom={0} left={0} right={0} p="$3" backgroundColor="white" borderTopWidth={1} borderColor="$gray5" borderTopLeftRadius="$8" borderTopRightRadius="$8">
                <XStack jc="space-between" ai="center">
                    <XStack gap="$2" ai="center">
                        <Ionicons name="cart" size={24} />
                        <Text fontWeight="bold">{totalQty} {t('suppliers.items', { defaultValue: 'items' })} · ₪{totalPrice}</Text>
                    </XStack>
                    <Button bg="$primary" color="white" hoverStyle={{ bg: "$third" }} pressStyle={{ bg: "$third" }} size="$3" theme="active" onPress={() => setOpen(true)}>
                        {t('suppliers.viewCart', { defaultValue: 'View Cart' })}
                    </Button>
                </XStack>
            </YStack>

            {/* Slide-up Sheet Cart */}
            <Sheet
                modal
                open={open}
                onOpenChange={setOpen}
                snapPoints={[100, 100]}
                dismissOnSnapToBottom
                >
                <Sheet.Frame f={1} p="$4" bg="white" pointerEvents="auto">
                    <CartPage
                        category={currentSupplier.category}
                        onClose={() => setOpen(false)}
                    />
                </Sheet.Frame>
                <Sheet.Overlay pointerEvents="auto" />
            </Sheet>
        </>
    );
};
