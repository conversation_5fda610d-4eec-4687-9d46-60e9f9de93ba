import { useState } from 'react';
import { <PERSON><PERSON>View, Alert } from 'react-native';
import {
  YStack,
  XStack,
  Text,
  Input,
  TextArea,
  Button,
  Separator,
  Label,
  Card,
  H4
} from 'tamagui';
import { Stack, useRouter } from 'expo-router';
import { useCartStore } from './CartStore';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useLastOrderStore, useSetOrderAddress } from './useLastOrderStore';
import { useMyOrdersStore } from './orders-page-components/useMyOrdersStore';
import { apiService } from '../../services/apiService';
import { useCurrentUserData } from '../useCurrentUserData';
import { showErrorAlert } from '../../utils/errorDisplay';
import { useTranslation } from 'react-i18next';

type Location = {
  lat: number;
  lng: number;
  address: string;
};

export function CheckoutPage({ category } : { category: string; }) {
  const { t } = useTranslation();
  const router = useRouter();
  const { user } = useCurrentUserData();

  const { getItemsBySupplier, totalPrice, clearCart } = useCartStore();
  const itemsBySupplier = getItemsBySupplier()

  let TotalWithoutFee = 0;
  let NumberOfOrders = 0;
  Object.entries(itemsBySupplier).map(([supplierId]) => {
    TotalWithoutFee += totalPrice(supplierId);
    NumberOfOrders++;
  });
  
  const deliveryFee = 12;                   // flat for now
  const [promo, setPromo] = useState('');
  const promoValue = promo === 'WASEL10' ? 10 : 0;
  const grandTotal = TotalWithoutFee + (deliveryFee * NumberOfOrders) - promoValue;

  // address, phone, notes
  const { address } = useSetOrderAddress();
  const [phone, setPhone] = useState('');
  const [note, setNote] = useState('');

  // payment
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card'>('cash');
  const [cardNumber, setCardNumber] = useState('');
  const [cardCvv, setCardCvv] = useState('');

  const isFormValid = () =>
    address && phone.trim() && (paymentMethod === 'cash' || cardNumber.trim());

  const { setLastOrderGroup } = useLastOrderStore();

  const placeOrder = async () => {
    if (!isFormValid()) {
      Alert.alert('Missing info', 'Please fill required fields.');
      return;
    }

    // Check if user is authenticated
    if (!user) {
      showErrorAlert(
        { message: 'Please log in to place an order.' },
        t
      );
      router.push('/auth/login');
      return;
    }

    const myOrders = useMyOrdersStore.getState();
    const placedAt = new Date().toISOString();

    try {
      // Split and create individual orders per supplier
      const newOrders = await Promise.all(
        Object.entries(itemsBySupplier).map(async ([supplierId, items]) => {
          const subTotal = totalPrice(supplierId);
          const total = subTotal + deliveryFee - promoValue;

          const lat = address?.lat || 32.2211;
          const lng = address?.lng || 35.2544;

          // Prepare order data for backend
          const orderData = {
            supplierId,
            supplierName: items[0]?.supplierName || 'Unknown Supplier',
            items: items.map(item => ({
              productId: item.product?.id || 'unknown',
              productName: item.product?.name || 'Unknown Product',
              productImage: item.product?.image || '',
              quantity: item.qty,
              price: item.finalPrice,
              selectedOptions: {
                size: item.selectedSize || '',
                color: item.selectedColor || '',
                additions: (item.selectedAdditions || []).map(addition => ({
                  id: addition.id || addition.name || '',
                  name: addition.name || '',
                  price: addition.price || 0
                })),
                without: item.without || [],
                sides: (item.selectedSides || []).map(side => ({
                  id: side.id || side.name || '',
                  name: side.name || '',
                  price: side.price || 0
                }))
              },
              subtotal: item.finalPrice
            })),
            subtotal: subTotal,
            deliveryFee,
            totalAmount: total,
            paymentMethod: paymentMethod as 'cash' | 'card' | 'wallet',
            deliveryAddress: {
              street: address?.address || 'No address provided',
              city: 'Nablus', // Default city
              coordinates: {
                lat: lat,
                lng: lng
              },
              notes: note
            },
            customerPhone: phone.trim() || 'No phone provided',
            notes: note
          };

          // Create order via backend API
          console.log('Creating order with data:', orderData);
          const response = await apiService.createOrder(orderData);

          if (!response.success) {
            throw new Error(response.message || 'Failed to create order');
          }

          const createdOrder = response.data;
          console.log('Order created successfully:', createdOrder);

          // Status mapping from backend to frontend
          const statusMap: Record<string, 'Pending' | 'Preparing' | 'On the Way' | 'Delivered'> = {
            'pending': 'Pending',
            'confirmed': 'Pending',
            'preparing': 'Preparing',
            'ready': 'Preparing',
            'out_for_delivery': 'On the Way',
            'delivered': 'Delivered',
            'cancelled': 'Delivered'
          };

          // Add to local store for immediate UI update using backend data
          myOrders.addOrder({
            id: createdOrder.orderId,
            createdAt: createdOrder.createdAt || placedAt,
            items,
            supplierId,
            total,
            status: statusMap[createdOrder.status] || 'Pending',
            supplierRecievedMoney: (paymentMethod === 'card'),
            address,
            phone,
            note,
            paymentMethod,
            promo,
            subTotal,
            deliveryFee,
            estimatedTime: createdOrder.estimatedDeliveryTime || '45-60 mins',
            driverName: createdOrder.driverName || '',
            driverPhone: createdOrder.driverPhone || '',
            driverLocation: {
              lng: 35.1137,
              lat: 32.6683,
            }
          });

          return {
            id: createdOrder.orderId,
            items,
            total,
            estimatedTime: '45-60 mins',
            address,
            phone,
            paymentMethod,
            placedAt,
            orderStatus: "Pending" as "Pending",
            driverName: "Ahmad Samer"
          };
        })
      );

      setLastOrderGroup(newOrders);
      clearCart();
      router.replace('/(customer-pages)/home/<USER>');
    } catch (error) {
      console.error('Error placing order:', error);
      showErrorAlert(error, t);
    }
  }

  return (
    <>
      <Stack.Screen options={{ title: 'Checkout', headerShown: true }} />

      <ScrollView contentContainerStyle={{ paddingBottom: 120 }}>
        <YStack gap="$4" p="$4">

          {/* 1. Order summary */}
          <Card p="$3" br="$6" elevation="$1">
            <H4 mb="$2">Order Summary</H4>
            {Object.entries(itemsBySupplier).map(([supplierId, items]) => (
              <YStack key={supplierId}>
                <Text fontWeight="bold">Supplier: {items[0]?.supplierName}</Text>
                {items.map(i => (
                  <XStack key={i.id} jc="space-between" ai="center" my="$1">
                    <Text>{i.qty} × {i.product.name}</Text>
                    <Text>₪{(i.finalPrice * i.qty).toFixed(2)}</Text>
                  </XStack>
                ))}
                <XStack jc="space-between">
                  <Text>Subtotal:</Text>
                  <Text>₪{totalPrice(supplierId).toFixed(2)}</Text>
                </XStack>
                <Separator my="$2" />
              </YStack>
            ))}
            <Button
              size="$2"
              icon={<Ionicons name="pencil" size={14} />}
              mt="$2"
              onPress={() => router.back()}   // or open cart sheet again
            >
              Edit Cart
            </Button>
          </Card>

          {/* 2. Address */}
          <YStack gap="$2" width={'100%'}>
            <Label>Delivery Address <Text color="red">*</Text></Label>
            <XStack gap="$2" width={'100%'} jc='space-between'>
              <Input
                width={'75%'}
                value={address?.address}
                placeholder="Set on map"
                rows={3}
              />
              <Button
                  width={'20%'}
                  size="$4"
                  onPress={() => router.push('/home/<USER>')}
              >
                Set
              </Button>
            </XStack>
          </YStack>

          {/* 3. Phone */}
          <YStack gap="$2">
            <Label>Contact Phone <Text color="red">*</Text></Label>
            <Input
              placeholder="e.g. 059-1234567"
              keyboardType="phone-pad"
              value={phone}
              onChangeText={setPhone}
            />
          </YStack>

          {/* 4. Notes */}
          <YStack gap="$2">
            <Label>Delivery Note (optional)</Label>
            <TextArea
              placeholder="Ring the bell twice..."
              value={note}
              onChangeText={setNote}
              rows={2}
            />
          </YStack>

          {/* 5. Payment */}
          <YStack gap="$2">
            <Label>Payment Method</Label>
            <XStack gap="$2">
              <Button
                theme={paymentMethod === 'cash' ? 'active' : undefined}
                onPress={() => setPaymentMethod('cash')}
                width="45%"
              >
                Cash
              </Button>
              <Button
                theme={paymentMethod === 'card' ? 'active' : undefined}
                onPress={() => setPaymentMethod('card')}
                width="45%"
              >
                Card
              </Button>
            </XStack>

            {paymentMethod === 'card' && (
              <YStack gap="$2" mt="$2">
                <Input
                  placeholder="Card Number"
                  keyboardType="number-pad"
                  value={cardNumber}
                  onChangeText={setCardNumber}
                />
                <Input
                  placeholder="CVV"
                  keyboardType="number-pad"
                  maxLength={4}
                  value={cardCvv}
                  onChangeText={setCardCvv}
                />
              </YStack>
            )}
          </YStack>

          {/* 6. Promo */}
          <YStack gap="$2">
            <Label>Promo Code</Label>
            <Input
              placeholder="WASEL10"
              value={promo}
              onChangeText={setPromo}
            />
            {promoValue > 0 && (
              <Text color="$green10">Promo applied: -₪{promoValue}</Text>
            )}
          </YStack>

          {/* 7. Cost breakdown */}
          <Separator my="$2" />
          <XStack jc="space-between">
            <Text>Sub-Total</Text>
            <Text>₪{TotalWithoutFee.toFixed(2)}</Text>
          </XStack>
          <XStack jc="space-between">
            <Text>Delivery Fee</Text>
            <Text>₪{deliveryFee.toFixed(2)} * {NumberOfOrders}</Text>
          </XStack>
          {promoValue > 0 && (
            <XStack jc="space-between">
              <Text>Promo Discount</Text>
              <Text color="$red10">-₪{promoValue.toFixed(2)}</Text>
            </XStack>
          )}
          <Separator my="$2" />
          <XStack jc="space-between" ai="center">
            <Text fontWeight="bold" fontSize="$6">Total</Text>
            <Text fontWeight="bold" fontSize="$6">₪{grandTotal.toFixed(2)}</Text>
          </XStack>

          {/* 8. Place order */}
          <Button
            disabled={!isFormValid()}
            opacity={isFormValid() ? 1 : 0.5}
            bg="$primary"
            br="$6"
            size="$6"
            onPress={placeOrder}
            hoverStyle={{ bg: "$third" }} 
            pressStyle={{ bg: "$third" }}
          >
            <MotiView
              from={{ opacity: 0.7, scale: 0.97 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'timing', duration: 200 }}
            >
              <Text color="white" fontWeight="700">
                Confirm & Pay ₪{grandTotal.toFixed(2)}
              </Text>
            </MotiView>
          </Button>
        </YStack>
      </ScrollView>
    </>
  )
}
